import { renderStandardPanel, renderCategoryPanel } from './components';
import { showAlert } from './error.js';

import { uploadCategoryCSV , uploadProductCSV, uploadSubProductCSV } from './importCSV.js';
export function setupNavigationHandlers() {
    const resetNavButtons = () => {
        ['productsBtn', 'subProductsBtn', 'categoriesBtn'].forEach(id => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.classList.remove('bg-lightdarkSidebar');
                btn.classList.add('hover:bg-gray-700');
            }
        });
    };

    // Sol sidebar butonları
    const sidebarProductsBtn = document.querySelector('#sidebar #productsBtn');
    const sidebarSubProductsBtn = document.querySelector('#sidebar #subProductsBtn');
    const sidebarCategoriesBtn = document.querySelector('#sidebar #categoriesBtn');

    // Üst topbar kartları
    const topbarProductsBtn = document.querySelector('.topbar #productsBtn');
    const topbarSubProductsBtn = document.querySelector('.topbar #subProductsBtn');
    const topbarCategoriesBtn = document.querySelector('.topbar #categoriesBtn');

    // Ürünler için event listener'lar
    [sidebarProductsBtn, topbarProductsBtn].forEach(btn => {
        btn?.addEventListener('click', () => {
            resetNavButtons();
            if (sidebarProductsBtn) {
                sidebarProductsBtn.classList.add('bg-lightdarkSidebar');
            }
            renderStandardPanel("Ürünleri");
        });
    });

    // Alt Ürünler için event listener'lar
    [sidebarSubProductsBtn, topbarSubProductsBtn].forEach(btn => {
        btn?.addEventListener('click', () => {
            resetNavButtons();
            if (sidebarSubProductsBtn) {
                sidebarSubProductsBtn.classList.add('bg-lightdarkSidebar');
            }
            renderStandardPanel("Alt Ürünleri");
        });
    });

    // Kategoriler için event listener'lar
    [sidebarCategoriesBtn, topbarCategoriesBtn].forEach(btn => {
        btn?.addEventListener('click', () => {
            resetNavButtons();
            if (sidebarCategoriesBtn) {
                sidebarCategoriesBtn.classList.add('bg-lightdarkSidebar');
            }
            renderCategoryPanel();
        });
    });
}

export function setupTabHandlers() {
    const exportTab = document.getElementById('exportTab');
    const importTab = document.getElementById('importTab');
    const exportContent = document.getElementById('exportContent');
    const importContent = document.getElementById('importContent');

    if (!exportTab || !importTab || !exportContent || !importContent) return;

    // Varsayılan olarak export açık
    handleExportClick();

    function clearTabStyles() {
        exportTab.classList.remove('border-b-4', 'border-red-600', 'text-red-600');
        exportTab.classList.add('text-gray-500');

        importTab.classList.remove('border-b-4', 'border-red-600', 'text-red-600');
        importTab.classList.add('text-gray-500');
    }

    function handleExportClick() {
        exportContent.classList.remove('hidden');
        importContent.classList.add('hidden');
        clearTabStyles();
        exportTab.classList.add('border-b-4', 'border-red-600', 'text-red-600');
        exportTab.classList.remove('text-gray-500');
    }

    function handleImportClick() {
        importContent.classList.remove('hidden');
        exportContent.classList.add('hidden');
        clearTabStyles();
        importTab.classList.add('border-b-4', 'border-red-600', 'text-red-600');
        importTab.classList.remove('text-gray-500');
    }

    exportTab.addEventListener('click', handleExportClick);
    importTab.addEventListener('click', handleImportClick);
}
export function clearAllTransfers() {
    const successList = document.getElementById('importSuccessList');
    const errorList = document.getElementById('importErrorList');
    successList.innerHTML = '';
    errorList.innerHTML = '';
}
export function setupFileInputHandlers() {
    const fileInput = document.getElementById('csvFileInput');
    const fileNameSpan = document.getElementById('fileNameText');
    const removeFileBtn = document.getElementById('removeFileBtn');

    if (!fileInput || !fileNameSpan || !removeFileBtn) return;

    // Önceki listener'ları temizle
    fileInput.removeEventListener('change', handleFileChange);
    removeFileBtn.removeEventListener('click', handleRemoveFile);

    function handleFileChange(event) {
        const fileName = event.target.files[0]?.name;
        if (fileName) {
            fileNameSpan.textContent = `"${fileName}" seçildi.`;
            removeFileBtn.classList.remove('hidden');
            clearAllTransfers();
        } else {
            fileNameSpan.textContent = '';
            removeFileBtn.classList.add('hidden');
        }
    }

    function handleRemoveFile() {
        fileInput.value = '';
        fileNameSpan.textContent = '';
        removeFileBtn.classList.add('hidden');
        clearAllTransfers();
    }

    fileInput.addEventListener('change', handleFileChange);
    removeFileBtn.addEventListener('click', handleRemoveFile);
}

export function setupUploadHandler(title) {
    const uploadBtn = document.getElementById('uploadBtn');
    const successList = document.getElementById('importSuccessList');
    const errorList = document.getElementById('importErrorList');

    if (!uploadBtn || !successList || !errorList)
        showAlert("Hata meydana geldi.","Yükleme butonu , başarı listesi ya da hata listesi yüklenirken hata meydana geldi.")    
    else {
        try {
            uploadBtn.removeEventListener('click', uploadCategoryCSV);
            uploadBtn.removeEventListener('click', uploadProductCSV);
            uploadBtn.removeEventListener('click', uploadSubProductCSV);
        
            if (title === undefined ) {
                uploadBtn.addEventListener('click', uploadCategoryCSV );
            }
            else if( title.includes("Alt Ürünleri")){
                uploadBtn.addEventListener('click', uploadSubProductCSV );
            }else if( title.includes("Ürünleri")){
                uploadBtn.addEventListener('click', uploadProductCSV );
            }
        } catch (error) {
            showAlert("İçeri Aktarılırken hata meydana geldi",error);
        }
    }
   
}
export function setupCsvPanelHandlers() {
    const csvButtons = document.querySelectorAll('.csv-section-btn');

    csvButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const section = btn.dataset.section;

            // Tüm butonlardan aktif stilleri kaldır
            csvButtons.forEach(b => {
                b.classList.remove('border-b-2', 'border-red-700', 'text-gray');
                b.classList.add('text-gray-700');
            });

            // Seçilen butona aktif stili ekle
            btn.classList.remove('text-gray-700');
            btn.classList.add('border-b-2', 'border-red-700', 'text-gray');

            // 🆕 İlgili paneli göster
            if (section === 'products' || section === 'subproducts') {
                renderStandardPanel(section === 'products' ? "Ürünleri" : "Alt Ürünleri");
            } else if (section === 'categories') {
                renderCategoryPanel();
            }
        });
    });
}

export function setupTopbarHandlers() {
    const topButtons = document.querySelectorAll('.csv-section-btn');
    
    topButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const section = btn.dataset.section;
            
            // İlgili paneli render et
            if (section === 'products' || section === 'subproducts') {
                renderStandardPanel(section);
            } else if (section === 'categories') {
                renderCategoryPanel();
            }
        });
    });
}

export function addToSelectedColumns(fieldName, displayName = null) {
    const selectedColumns = document.getElementById('selectedColumns');
    const availableColumns = document.getElementById('availableColumns');
    
    // displayName yoksa fieldName'i kullan
    const showName = displayName || fieldName;
    
    // Aynı field'dan varsa tekrar ekleme
    const exists = Array.from(selectedColumns.children).some(li => li.dataset.title === fieldName);
    if (exists) return;

    const li = document.createElement('li');
    li.dataset.title = fieldName; // Orijinal field adını sakla
    li.className = 'flex justify-between items-center bg-white p-2 rounded';
    li.innerHTML = `
        <span>${showName}</span>
        <button class="text-red-500 hover:text-red-700 rounded ml-4 remove-btn text-[#EA3323]"><span class="material-symbols-outlined">
        cancel
        </span></button>
    `;

    // Silme butonuna event
    li.querySelector('.remove-btn').onclick = () => {
        if (fieldName === 'code' ) return;
        if (fieldName === 'main_code') return;
        li.remove();

        const backLi = document.createElement('li');
        backLi.className = 'flex justify-between items-center bg-white hover:bg-gray-50 p-2 rounded';
        backLi.innerHTML = `
            <span>${showName}</span>
            <button class="text-green-600 hover:text-green-800 ml-4 add-btn"><span class="material-symbols-outlined text-[#75FB4C]">
                    add_circle
                    </span></button>
        `;
        backLi.querySelector('.add-btn').onclick = () => addToSelectedColumns(fieldName, showName);

        availableColumns.appendChild(backLi);
    };

    selectedColumns.appendChild(li);

    // Sol taraftan kaldır
    const leftItems = Array.from(availableColumns.children);
    leftItems.forEach(item => {
        const text = item.querySelector('span')?.textContent;
        if (text === showName) item.remove();
    });
}
export function setupMobileMenuHandler() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const closeSidebarBtn = document.getElementById('closeSidebarBtn');
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('mobileOverlay');

    if (!mobileMenuBtn || !sidebar || !overlay) return;

    // Sidebar'ı açma
    mobileMenuBtn.addEventListener('click', () => {
        sidebar.classList.add('active');
        overlay.classList.add('active');
    });

    // Sidebar'ı kapatma - overlay'e tıklayınca
    overlay.addEventListener('click', () => {
        sidebar.classList.remove('active');
        overlay.classList.remove('active');
    });

    // Sidebar'ı kapatma - X butonuna tıklayınca
    if (closeSidebarBtn) {
        closeSidebarBtn.addEventListener('click', () => {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        });
    }

    // ESC tuşuna basınca kapatma
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && sidebar.classList.contains('active')) {
            sidebar.classList.remove('active');
            overlay.classList.remove('active');
        }
    });
}

export function setupSelectAllHandlers() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    const removeAllBtn = document.getElementById('removeAllBtn');
    
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', () => {
            const availableColumns = document.getElementById('availableColumns');
            const items = Array.from(availableColumns.children);
            
            items.forEach(item => {
                const addBtn = item.querySelector('.add-btn');
                if (addBtn) {
                    addBtn.click();
                }
            });
        });
    }
    
    if (removeAllBtn) {
        removeAllBtn.addEventListener('click', () => {
            const selectedColumns = document.getElementById('selectedColumns');
            const items = Array.from(selectedColumns.children);
            
            items.forEach(item => {
                // ürünler ve alt ürünlerdeki code kısımları çakışıyor önlemek
                if (item.dataset.title === 'code') return;
                if (item.dataset.title === 'main_code') return;
                const removeBtn = item.querySelector('.remove-btn');
                if (removeBtn) {
                    removeBtn.click();
                }
            });
        });
    }
}

export function setupLogoutHandler() {
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            // Token'ı localStorage'dan sil
            localStorage.removeItem('token');
            
            // Ana sayfaya yönlendir
            window.location.href = '/';
        });
    }
}

// Make sure switchPanel is available immediately
window.switchPanel = function(panelType) {
    const buttons = {
        'products': 'productsBtn',
        'subproducts': 'subProductsBtn', 
        'categories': 'categoriesBtn'
    };
    
    const buttonId = buttons[panelType];
    if (buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.click();
        }
    }
};
